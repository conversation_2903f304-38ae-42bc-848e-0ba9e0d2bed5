<?php

namespace App\Controllers;

use App\Models\UserLogModel;

class UserLogController extends BaseController
{
    protected $UserLogModel;

    public function __construct()
    {
        $this->UserLogModel = new UserLogModel();
    }

    public function index()
    {
        $data['title'] = 'userlogs';
        $data['userlogs'] = $this->UserLogModel->findAll();
        
        return view('userlogs/index', $data);
    }

    public function create()
    {
        $data['title'] = 'Create UserLog';
        
        if ($this->request->getMethod() === 'post') {
            $rules = [
                // Add your validation rules here
            ];

            if (!$this->validate($rules)) {
                $data['validation'] = $this->validator;
            } else {
                $this->UserLogModel->save($this->request->getPost());
                return redirect()->to('/userlogs')->with('message', 'UserLog created successfully');
            }
        }

        return view('userlogs/create', $data);
    }

    public function edit($id = null)
    {
        $data['title'] = 'Edit UserLog';
        $data['UserLog'] = $this->UserLogModel->find($id);

        if (empty($data['UserLog'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Cannot find the UserLog with id: ' . $id);
        }

        if ($this->request->getMethod() === 'post') {
            $rules = [
                // Add your validation rules here
            ];

            if (!$this->validate($rules)) {
                $data['validation'] = $this->validator;
            } else {
                $this->UserLogModel->update($id, $this->request->getPost());
                return redirect()->to('/userlogs')->with('message', 'UserLog updated successfully');
            }
        }

        return view('userlogs/edit', $data);
    }

    public function delete($id = null)
    {
        if ($this->UserLogModel->delete($id)) {
            return redirect()->to('/userlogs')->with('message', 'UserLog deleted successfully');
        }
        
        return redirect()->to('/userlogs')->with('error', 'Failed to delete UserLog');
    }

    public function view($id = null)
    {
        $data['title'] = 'View UserLog';
        $data['UserLog'] = $this->UserLogModel->find($id);

        if (empty($data['UserLog'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Cannot find the UserLog with id: ' . $id);
        }

        return view('userlogs/view', $data);
    }
}