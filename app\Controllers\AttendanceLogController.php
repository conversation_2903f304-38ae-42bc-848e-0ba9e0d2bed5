<?php

namespace App\Controllers;

use App\Models\AttendanceLogModel;

class AttendanceLogController extends BaseController
{
    protected $AttendanceLogModel;

    public function __construct()
    {
        $this->AttendanceLogModel = new AttendanceLogModel();
    }

    public function index()
    {
        $data['title'] = 'Attendance Logs';

        // Get filters from request
        $filters = [
            'search' => $this->request->getGet('search'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to'),
            'verifymode' => $this->request->getGet('verifymode'),
            'inoutmode' => $this->request->getGet('inoutmode'),
            'sn' => $this->request->getGet('sn')
        ];

        // Get attendance logs with pagination
        $attendanceLogsBuilder = $this->AttendanceLogModel->getLogsWithStudents($filters);
        $data['attendancelogs'] = $attendanceLogsBuilder->paginate(20);
        $data['pager'] = $this->AttendanceLogModel->pager;
        $data['filters'] = $filters;

        return view('attendancelogs/index', $data);
    }

    public function create()
    {
        $data['title'] = 'Create AttendanceLog';
        
        if ($this->request->getMethod() === 'post') {
            $rules = [
                // Add your validation rules here
            ];

            if (!$this->validate($rules)) {
                $data['validation'] = $this->validator;
            } else {
                $this->AttendanceLogModel->save($this->request->getPost());
                return redirect()->to('/attendancelogs')->with('message', 'AttendanceLog created successfully');
            }
        }

        return view('attendancelogs/create', $data);
    }

    public function edit($id = null)
    {
        $data['title'] = 'Edit AttendanceLog';
        $data['AttendanceLog'] = $this->AttendanceLogModel->find($id);

        if (empty($data['AttendanceLog'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Cannot find the AttendanceLog with id: ' . $id);
        }

        if ($this->request->getMethod() === 'post') {
            $rules = [
                // Add your validation rules here
            ];

            if (!$this->validate($rules)) {
                $data['validation'] = $this->validator;
            } else {
                $this->AttendanceLogModel->update($id, $this->request->getPost());
                return redirect()->to('/attendancelogs')->with('message', 'AttendanceLog updated successfully');
            }
        }

        return view('attendancelogs/edit', $data);
    }

    public function delete($id = null)
    {
        if ($this->AttendanceLogModel->delete($id)) {
            return redirect()->to('/attendancelogs')->with('message', 'AttendanceLog deleted successfully');
        }
        
        return redirect()->to('/attendancelogs')->with('error', 'Failed to delete AttendanceLog');
    }

    public function view($encodedKey = null)
    {
        $data['title'] = 'View Attendance Log';

        // Decode the composite key (sn|scan_date|pin)
        $keyParts = explode('|', base64_decode($encodedKey));
        if (count($keyParts) !== 3) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Invalid attendance log key');
        }

        $data['AttendanceLog'] = $this->AttendanceLogModel
            ->where('sn', $keyParts[0])
            ->where('scan_date', $keyParts[1])
            ->where('pin', $keyParts[2])
            ->first();

        if (empty($data['AttendanceLog'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Cannot find the attendance log');
        }

        return view('attendancelogs/view', $data);
    }
}