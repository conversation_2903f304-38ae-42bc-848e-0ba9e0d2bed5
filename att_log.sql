/*
 Navicat Premium Data Transfer

 Source Server         : LOCALHOST
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42)
 Source Host           : localhost:3306
 Source Schema         : fin_pro

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42)
 File Encoding         : 65001

 Date: 08/06/2025 22:37:36
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for att_log
-- ----------------------------
DROP TABLE IF EXISTS `att_log`;
CREATE TABLE `att_log`  (
  `sn` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `scan_date` datetime NOT NULL,
  `pin` varchar(32) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `verifymode` int(11) NOT NULL,
  `inoutmode` int(11) NOT NULL DEFAULT 0,
  `reserved` int(11) NOT NULL DEFAULT 0,
  `work_code` int(11) NOT NULL DEFAULT 0,
  `att_id` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  PRIMARY KEY (`sn`, `scan_date`, `pin`) USING BTREE,
  INDEX `pin`(`pin`) USING BTREE,
  INDEX `sn`(`sn`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of att_log
-- ----------------------------
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 19:14:04', '1', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 19:15:09', '1', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 19:16:20', '1', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 19:18:49', '1', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 21:04:45', '1', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 21:32:46', '1', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 21:33:56', '1', 20, 3, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 21:34:59', '1', 20, 2, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 21:36:26', '1', 20, 2, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 21:49:45', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 21:50:53', '2', 20, 3, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 21:51:04', '1', 20, 3, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 21:54:00', '2', 20, 3, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 21:55:10', '1', 20, 3, 0, 0, '0');
INSERT INTO `att_log` VALUES ('66208023321907', '2025-02-15 21:59:24', '1', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2024-12-18 14:51:28', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2024-12-18 14:54:11', '3', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2024-12-18 14:56:30', '6', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2024-12-18 14:59:37', '7', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2024-12-18 15:28:30', '7', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2024-12-18 15:40:55', '7', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-08 12:54:59', '7', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-08 13:03:49', '5', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-08 13:04:00', '7', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-10 08:53:51', '7', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-10 08:56:43', '1', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-11 07:14:37', '7', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-11 07:29:55', '7', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-11 10:00:44', '7', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-13 07:33:31', '7', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-13 07:35:48', '1015297', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-13 07:35:56', '1013219', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-13 08:07:45', '1013219', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-13 08:07:48', '1015297', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-13 08:09:41', '1015297', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-13 08:09:53', '1013219', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-13 08:11:09', '1015297', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-13 10:16:31', '2', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-14 07:19:28', '7', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-14 09:52:08', '2', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-15 07:17:51', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-15 07:18:07', '3', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-15 07:33:30', '6', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-16 07:27:10', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-16 07:27:18', '3', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-16 09:51:59', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-16 09:53:06', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-16 09:53:57', '1', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-16 10:47:45', '2', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-16 11:03:00', '7', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 07:33:32', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 07:33:49', '1', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:28:39', '1012200', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:47:44', '1014269', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:47:47', '1014280', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:47:49', '1014273', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:47:53', '1013241', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:47:56', '1015287', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:47:59', '1015295', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:48:02', '1015294', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:48:15', '1014260', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:48:20', '1013242', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:48:23', '1013240', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:48:27', '1012201', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:48:29', '1014265', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:48:33', '1013255', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:48:37', '1013225', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:48:41', '1013236', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:48:44', '1013226', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:48:47', '1014266', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:48:50', '1014262', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:49:11', '1014284', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:49:16', '1014259', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:49:20', '1014264', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:49:23', '1013220', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:49:29', '1015293', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:49:35', '1014263', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:49:40', '1013219', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:49:43', '1013251', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:50:08', '1013239', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:50:10', '1014261', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:50:13', '1014257', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:50:15', '1013249', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:50:22', '1012200', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:50:24', '1013253', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:50:27', '1013252', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:50:30', '1012214', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:50:33', '1013243', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:50:36', '1014279', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:50:38', '1015289', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:51:18', '1013246', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:51:23', '1014277', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:51:34', '1015296', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:51:36', '1015300', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:51:40', '1015297', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:51:43', '1014278', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:51:47', '1014275', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:51:50', '1015290', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:51:53', '1014271', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:51:58', '1015288', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:52:01', '1014285', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:52:04', '1014281', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:52:09', '1015286', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:52:14', '1015292', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:52:17', '1014268', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:52:20', '1015291', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:52:24', '1014283', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:52:34', '1015294', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:52:38', '1015295', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:52:45', '1013219', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:53:05', '1014260', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-17 12:53:10', '6', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:13:32', '6', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:13:42', '1', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:14:49', '4', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:14:55', '3', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:16:17', '1013255', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:19:02', '1015291', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:19:45', '1014278', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:23:24', '6', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:24:33', '1013220', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:24:51', '1013219', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:25:00', '1013251', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:25:46', '1012200', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:26:45', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:27:08', '1013226', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:27:35', '1014285', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:27:45', '1015286', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:28:06', '1013253', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:28:25', '1014264', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:29:18', '1014261', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:29:38', '1013243', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:30:19', '1014280', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:30:41', '1012214', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:31:00', '1013242', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:31:31', '1015300', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:31:55', '1015290', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:32:27', '1014266', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:33:10', '1015288', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:33:43', '1014257', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:34:22', '1014263', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:35:54', '1014268', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:36:39', '1014269', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:36:57', '1013239', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:37:25', '1013240', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:38:18', '1015297', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:38:34', '1015296', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:39:15', '1012201', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:39:35', '1013236', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:39:51', '1015294', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:40:04', '1015295', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:40:12', '1015293', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:40:34', '1015289', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:41:01', '1013252', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:41:13', '1014275', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:41:26', '1014283', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:41:53', '1014265', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:42:27', '1013246', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:42:49', '1014284', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:44:28', '1013241', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 07:49:48', '1014271', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 09:39:59', '1014281', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 10:47:10', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 11:20:05', '1013219', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 11:27:07', '6', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 12:38:16', '7', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-20 13:39:17', '1', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:13:47', '1013226', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:14:12', '1013219', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:14:26', '1013255', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:14:37', '1014285', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:14:42', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:14:49', '3', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:14:58', '1013241', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:16:55', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:18:16', '1012214', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:19:37', '1015290', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:19:46', '4', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:20:53', '1015287', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:21:53', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:23:29', '1014278', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:23:43', '1013252', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:24:28', '1013253', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:24:45', '1012201', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:24:54', '1015288', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:26:20', '1013240', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:26:25', '1013243', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:26:38', '1014261', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:27:15', '1015294', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:27:31', '1013251', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:27:43', '1015286', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:28:01', '1014266', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:28:32', '1012200', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:29:06', '1015292', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:30:00', '1015300', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:30:41', '1014264', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:30:57', '1014268', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:31:47', '1015291', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:32:04', '1013220', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:32:25', '1013249', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:32:55', '1014269', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:34:00', '1014257', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:34:22', '1013239', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:34:32', '1015295', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:34:41', '1014280', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:35:08', '1015297', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:35:21', '7', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:35:24', '1015293', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:35:41', '1015296', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:36:22', '1014263', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:41:44', '1013246', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:42:25', '1', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:43:09', '1013242', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:43:50', '1013236', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:45:05', '1014283', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:47:24', '1014265', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:48:22', '5', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:50:09', '1014260', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:50:14', '1014262', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-21 07:53:27', '1014279', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:16:37', '6', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:16:43', '3', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:17:10', '4', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:18:00', '1013226', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:18:11', '1014285', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:19:07', '1013220', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:19:42', '1013225', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:19:51', '1013255', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:20:18', '1015292', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:20:28', '1014277', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:21:11', '1013219', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:21:25', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:21:43', '1015287', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:22:01', '1015294', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:23:43', '1014278', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:25:17', '1014261', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:25:41', '1015286', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:26:12', '1015290', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:26:57', '1014266', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:27:53', '1014257', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:28:14', '1015290', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:28:28', '1015291', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:28:39', '1013253', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:28:51', '1012200', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:29:11', '1014264', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:30:13', '1015300', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:30:26', '1014268', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:30:44', '1013249', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:31:06', '1013241', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:31:18', '1015293', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:31:38', '1013251', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:32:17', '1014280', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:32:39', '1014259', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:33:16', '1015296', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:34:08', '1014284', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:35:31', '1013239', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:35:44', '1013243', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:36:00', '1012214', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:36:15', '1014269', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:37:10', '1012201', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:38:15', '1015288', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:39:48', '1014263', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:39:53', '1013242', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:40:06', '1013240', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:40:53', '1013246', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:42:27', '1014283', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:43:01', '1013236', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:44:38', '1015289', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:45:40', '1014262', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:48:19', '1015297', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 07:49:00', '5', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 08:11:29', '1014279', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 08:11:34', '7', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 08:19:56', '1014281', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 09:48:33', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-22 09:49:33', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:14:35', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:14:43', '4', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:15:09', '1013219', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:15:40', '3', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:17:02', '1013239', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:20:57', '1015291', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:21:59', '3', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:23:28', '1014285', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:24:18', '1015294', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:24:43', '1014261', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:25:05', '1014264', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:25:33', '1015287', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:25:39', '1013226', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:25:48', '1015286', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:25:53', '6', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:27:16', '1014259', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:27:42', '1014266', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:29:06', '1013225', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:29:21', '1015292', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:30:13', '1012200', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:30:58', '1015300', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:31:38', '1014284', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:32:12', '1012214', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:32:26', '1013242', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:32:34', '1014260', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:32:50', '1013251', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:33:00', '1013220', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:33:32', '1014268', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:34:08', '1013240', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:34:14', '1014283', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:35:36', '1015295', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:35:45', '1014280', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:35:55', '1015290', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:36:06', '1014269', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:36:33', '1014278', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:37:33', '1015289', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:37:45', '1015297', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:38:01', '1015288', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:38:35', '1014265', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:39:01', '1013249', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:39:41', '1012201', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:40:47', '1013241', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:41:26', '1013255', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:42:16', '1', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:42:22', '1013246', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:42:28', '1013243', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:42:57', '1015296', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:43:14', '1015293', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:43:51', '1014271', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:45:49', '1014262', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:46:02', '1013236', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:46:16', '1015288', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 07:50:47', '1013252', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 08:01:15', '1014279', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-23 08:01:31', '5', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:14:06', '3', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:14:14', '4', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:14:27', '1013239', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:14:58', '1013220', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:19:57', '1013226', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:20:08', '1014285', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:20:14', '7', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:20:17', '1015294', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:20:26', '1015297', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:20:39', '1013241', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:23:12', '1015287', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:23:31', '1013255', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:24:00', '1015290', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:24:07', '1012200', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:24:40', '1013253', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:26:05', '1014278', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:26:35', '1014259', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:26:49', '1013240', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:27:44', '1014264', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:29:23', '1013243', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:29:32', '1014284', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:29:41', '1015289', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:29:57', '1015300', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:30:14', '1012201', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:30:51', '1014266', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:30:56', '1014275', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:33:05', '1013225', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:33:53', '1013249', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:34:04', '1013251', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:34:10', '1015291', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:34:16', '1014269', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:34:32', '1014280', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:34:52', '1014268', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:35:20', '1013242', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:35:31', '1015286', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:36:06', '1012214', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:36:22', '1014283', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:36:28', '1014278', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:36:44', '1013236', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:37:04', '2', 20, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:37:52', '1014257', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:38:17', '1014275', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:39:04', '1015296', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:39:14', '1015293', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:40:10', '1013219', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:42:00', '1014263', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:44:47', '1014271', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:47:58', '1014262', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 07:53:56', '1015293', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 08:02:17', '1014279', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-24 08:11:26', '1', 1, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-25 16:59:42', '1014273', 3, 1, 0, 0, '0');
INSERT INTO `att_log` VALUES ('FIO66205020150662', '2025-01-25 17:01:17', '1014273', 3, 1, 0, 0, '0');

-- ----------------------------
-- Table structure for user_log
-- ----------------------------
DROP TABLE IF EXISTS `user_log`;
CREATE TABLE `user_log`  (
  `login_id` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `log_date` datetime NOT NULL,
  `module` int(11) NOT NULL COMMENT '0: Pengaturan, 1: Pegawai, 2: Mesin, 3: Pengecualian, 4: Laporan, 5: Proses',
  `tipe_log` tinyint(4) NOT NULL COMMENT '0: Tambah, 1: Ubah, 2: Hapus, 3: Buka Pintu',
  `nama_data` varchar(250) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `log_note` varchar(300) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_log
-- ----------------------------
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:10:00', 5, 1, '', 'Login user aplikasi \"admin\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:10:00', 5, 1, '', 'Login user aplikasi \"admin\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:14:00', 5, 1, '', 'Login user aplikasi \"admin\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:15:00', 5, 2, '', 'Mengubah pengaturan umum');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:15:00', 5, 2, '', 'Mengubah pengaturan umum');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:17:00', 5, 2, '', 'Mengubah pengaturan umum');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:25:00', 5, 1, '', 'Menambah jam kerja dengan nama=07.00-13.00');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:26:00', 5, 1, '', 'Menambah jam kerja dengan nama=08.30');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:27:00', 5, 1, '', 'Menambah jadwal kerja normal dengan nama=PENDIDIK');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:28:00', 5, 1, '', 'Menambah jadwal kerja normal dengan nama=PESERTA DIDIK');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:30:00', 5, 1, '', 'Menambah kategori izin dengan nama=Dinas Luar (Pelatihan)');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:33:00', 5, 1, '', 'Menambah pembagian pegawai 2 dengan nama=Kelas TA A 2');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:33:00', 5, 1, '', 'Menambah pembagian pegawai 2 dengan nama=Kelas TA B 1');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:34:00', 5, 1, '', 'Menambah pembagian pegawai 2 dengan nama=Kelas TA B2');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:40:00', 5, 1, '', 'Menambah/mengubah mesin dengan nama=Mesin 1');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 3, 6, '', 'Tampilkan data user di mesin \"Mesin 1\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download pegawai PIN \"2\" - Baru');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download pegawai PIN \"3\" - Baru');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download pegawai PIN \"6\" - Baru');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download pegawai PIN \"7\" - Baru');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download FP pegawai PIN \"2\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download FP pegawai PIN \"2\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download FP pegawai PIN \"2\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download FP pegawai PIN \"3\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download FP pegawai PIN \"3\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download FP pegawai PIN \"3\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download FP pegawai PIN \"6\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download FP pegawai PIN \"6\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download FP pegawai PIN \"6\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download FP pegawai PIN \"7\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download FP pegawai PIN \"7\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:53:00', 1, 1, '', 'Download FP pegawai PIN \"7\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 3, 6, '', 'Tampilkan data user di mesin \"Mesin 1\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 2, '', 'Download pegawai PIN \"2\" - Ubah');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 2, '', 'Download pegawai PIN \"3\" - Ubah');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 2, '', 'Download pegawai PIN \"6\" - Ubah');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 2, '', 'Download pegawai PIN \"7\" - Ubah');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 1, '', 'Download FP pegawai PIN \"2\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 1, '', 'Download FP pegawai PIN \"2\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 1, '', 'Download FP pegawai PIN \"2\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 1, '', 'Download FP pegawai PIN \"3\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 1, '', 'Download FP pegawai PIN \"3\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 1, '', 'Download FP pegawai PIN \"3\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 1, '', 'Download FP pegawai PIN \"6\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 1, '', 'Download FP pegawai PIN \"6\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 1, '', 'Download FP pegawai PIN \"6\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 1, '', 'Download FP pegawai PIN \"7\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 1, '', 'Download FP pegawai PIN \"7\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:54:00', 1, 1, '', 'Download FP pegawai PIN \"7\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:55:00', 1, 1, '', 'Mengubah pegawai dengan PIN=2 dan NIP=2');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:56:00', 1, 1, '', 'Mengubah pegawai dengan PIN=3 dan NIP=');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:56:00', 1, 1, '', 'Mengubah pegawai dengan PIN=6 dan NIP=');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 14:57:00', 1, 1, '', 'Mengubah pegawai dengan PIN=7 dan NIP=');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:03:00', 5, 1, '', 'Import 3 data pegawai dari file excel');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:05:00', 1, 1, '', 'Mengubah pegawai dengan PIN=2 dan NIP=2');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:05:00', 1, 1, '', 'Mengubah pegawai dengan PIN=3 dan NIP=3');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:05:00', 1, 1, '', 'Mengubah pegawai dengan PIN=6 dan NIP=6');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:05:00', 1, 1, '', 'Mengubah pegawai dengan PIN=7 dan NIP=7');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 6, '', 'Tampilkan data pegawai di database ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user FP di mesin \"Mesin 1\", PIN \"2\", index \"12\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user FP di mesin \"Mesin 1\", PIN \"2\", index \"1\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user FP di mesin \"Mesin 1\", PIN \"2\", index \"0\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user FP di mesin \"Mesin 1\", PIN \"3\", index \"12\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user FP di mesin \"Mesin 1\", PIN \"3\", index \"1\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user FP di mesin \"Mesin 1\", PIN \"3\", index \"0\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"4\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"5\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user FP di mesin \"Mesin 1\", PIN \"6\", index \"12\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user FP di mesin \"Mesin 1\", PIN \"6\", index \"1\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user FP di mesin \"Mesin 1\", PIN \"6\", index \"0\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user FP di mesin \"Mesin 1\", PIN \"7\", index \"12\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user FP di mesin \"Mesin 1\", PIN \"7\", index \"1\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:06:00', 3, 1, '', 'Upload user FP di mesin \"Mesin 1\", PIN \"7\", index \"0\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:08:00', 3, 1, '', 'Download scanlog dari mesin \"Mesin 1\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:09:00', 4, 6, '', 'Proses laporan');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:10:00', 5, 2, '', 'Mengubah jam kerja dengan nama=07.00-13.00');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:10:00', 4, 6, '', 'Proses laporan');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:17:00', 3, 1, '', 'Download scanlog dari mesin \"Mesin 1\" ');
INSERT INTO `user_log` VALUES ('admin', '2024-12-18 15:19:00', 4, 6, '', 'Proses laporan');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:40:00', 5, 1, '', 'Login user aplikasi \"admin\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:49:00', 5, 1, '', 'Import 56 data pegawai dari file excel');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:52:00', 3, 1, '', 'Download scanlog dari mesin \"Mesin 1\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:52:00', 3, 6, '', 'Tampilkan data pegawai di database ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1012200\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1012201\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1012214\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013219\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013220\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013225\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013226\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013236\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013239\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013240\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013241\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013242\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013243\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013246\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013249\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013251\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013252\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013253\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1013255\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014257\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014259\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014260\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014261\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014262\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014263\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014264\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014265\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014266\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014268\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014269\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014271\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014272\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014273\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014274\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014275\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014277\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014278\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014279\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014280\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014281\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014283\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014284\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1014285\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1015286\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1015287\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1015288\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1015289\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1015290\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1015291\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1015292\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1015293\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1015294\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1015295\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1015296\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1015297\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-10 08:53:00', 3, 1, '', 'Upload user RFID \"0\" di mesin \"Mesin 1\", PIN \"1015300\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-25 17:05:00', 5, 1, '', 'Login user aplikasi \"admin\" ');
INSERT INTO `user_log` VALUES ('admin', '2025-01-25 17:08:00', 3, 4, '', 'Export excel data mesin');
INSERT INTO `user_log` VALUES ('admin', '2025-01-25 17:09:00', 3, 1, '', 'Download scanlog dari mesin \"Mesin 1\" ');

-- ----------------------------
-- Table structure for dev_type
-- ----------------------------
DROP TABLE IF EXISTS `dev_type`;
CREATE TABLE `dev_type`  (
  `dev_type` int(11) NOT NULL,
  `id_type` int(11) NOT NULL,
  `type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dev_type
-- ----------------------------
INSERT INTO `dev_type` VALUES (0, 1, 'New Premier Series');
INSERT INTO `dev_type` VALUES (0, 2, 'Elegant Series');
INSERT INTO `dev_type` VALUES (0, 3, 'Hybrid+ Series');
INSERT INTO `dev_type` VALUES (2, 4, 'Neo-151NC');
INSERT INTO `dev_type` VALUES (2, 5, 'SF-1000CNB');
INSERT INTO `dev_type` VALUES (3, 6, 'Revo 151 BNC');
INSERT INTO `dev_type` VALUES (2, 7, 'Neo-A152 NC');
INSERT INTO `dev_type` VALUES (3, 8, 'Revo FF 153 BNC');
INSERT INTO `dev_type` VALUES (4, 9, 'Livo 151 B');
INSERT INTO `dev_type` VALUES (3, 10, 'Revo D-152B');
INSERT INTO `dev_type` VALUES (3, 11, 'Revo FF-157NB');
INSERT INTO `dev_type` VALUES (3, 12, 'Revo-155BNC');
INSERT INTO `dev_type` VALUES (3, 13, 'Revo-156BNC');
INSERT INTO `dev_type` VALUES (3, 14, 'Revo Duo-158BNC');
INSERT INTO `dev_type` VALUES (0, 15, 'Hybrid Pro Series');
INSERT INTO `dev_type` VALUES (3, 16, 'Revo-160B');
INSERT INTO `dev_type` VALUES (3, 17, 'Revo-161B');
INSERT INTO `dev_type` VALUES (3, 18, 'Revo FF-162BNC');
INSERT INTO `dev_type` VALUES (3, 19, 'Revo-163BNC');
INSERT INTO `dev_type` VALUES (0, 20, 'New Hybrid Pro Series');
INSERT INTO `dev_type` VALUES (3, 21, 'Revo FF-181BNC');
INSERT INTO `dev_type` VALUES (3, 22, 'Revo FF-182BNC');
INSERT INTO `dev_type` VALUES (3, 23, 'Revo FF-183');
INSERT INTO `dev_type` VALUES (3, 24, 'Revo-180');
INSERT INTO `dev_type` VALUES (3, 25, 'Revo-185BNC');
INSERT INTO `dev_type` VALUES (5, 26, 'HIK');
INSERT INTO `dev_type` VALUES (2, 27, 'Neo W-201BNC');
INSERT INTO `dev_type` VALUES (3, 28, 'Revo WD-203BNC');
INSERT INTO `dev_type` VALUES (3, 29, 'Revo WF-205BNC');
INSERT INTO `dev_type` VALUES (3, 31, 'Revo WF-206BNC');
INSERT INTO `dev_type` VALUES (3, 32, 'Revo 201B');
INSERT INTO `dev_type` VALUES (3, 33, 'Revo W-202BNC');
INSERT INTO `dev_type` VALUES (3, 34, 'Revo WFA-207NC');
INSERT INTO `dev_type` VALUES (3, 35, 'Revo WDV-204BNC');
INSERT INTO `dev_type` VALUES (3, 36, 'Revo WFV-208BNC');
INSERT INTO `dev_type` VALUES (3, 37, 'Revo W-231N');
INSERT INTO `dev_type` VALUES (3, 38, 'Revo A-232C');
INSERT INTO `dev_type` VALUES (3, 39, 'Revo WFV-234NCA');
INSERT INTO `dev_type` VALUES (3, 40, 'Revo W-230N');

-- ----------------------------
-- Table structure for kategori_izin
-- ----------------------------
DROP TABLE IF EXISTS `kategori_izin`;
CREATE TABLE `kategori_izin`  (
  `kat_izin_id` int(11) NOT NULL DEFAULT 0,
  `kat_izin_nama` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `izin_jenis_id` smallint(6) NULL DEFAULT 0,
  PRIMARY KEY (`kat_izin_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kategori_izin
-- ----------------------------
INSERT INTO `kategori_izin` VALUES (1, 'Kebanjiran', 10);
INSERT INTO `kategori_izin` VALUES (2, 'Mengurus NPWP', 20);
INSERT INTO `kategori_izin` VALUES (3, 'Sepeda Motor Mogok', 30);
INSERT INTO `kategori_izin` VALUES (4, 'Sakit Rawat Jalan', 40);
INSERT INTO `kategori_izin` VALUES (5, 'Disetujui Atasan', 50);
INSERT INTO `kategori_izin` VALUES (6, 'Mengurus BPJS Pegawai', 60);
INSERT INTO `kategori_izin` VALUES (7, 'Training Ke Kantor Cabang', 70);
INSERT INTO `kategori_izin` VALUES (8, 'Delay Penerbangan', 71);
INSERT INTO `kategori_izin` VALUES (9, 'Mengikuti Jadwal Keberangkatan Pesawat', 72);
INSERT INTO `kategori_izin` VALUES (10, 'Cuti Normatif Nikah Disetujui Atasan', 80);
INSERT INTO `kategori_izin` VALUES (11, 'Cuti Disetujui Atasan', 90);
INSERT INTO `kategori_izin` VALUES (12, 'Lupa Scan Masuk', 100);
INSERT INTO `kategori_izin` VALUES (13, 'Lupa Scan Pulang', 101);
INSERT INTO `kategori_izin` VALUES (14, 'Lupa Scan Mulai Istirahat', 102);
INSERT INTO `kategori_izin` VALUES (15, 'Lupa Scan Selesai Istirahat', 103);
INSERT INTO `kategori_izin` VALUES (16, 'Lupa Scan Mulai Lembur', 104);
INSERT INTO `kategori_izin` VALUES (17, 'Lupa Scan Selesai Lembur', 105);
INSERT INTO `kategori_izin` VALUES (18, 'Bencana Alam', 110);
INSERT INTO `kategori_izin` VALUES (19, 'Keperluan Pribadi', 120);
INSERT INTO `kategori_izin` VALUES (20, 'Disetujui SPV', 90);
INSERT INTO `kategori_izin` VALUES (21, 'Dinas Luar (Pelatihan)', 70);
INSERT INTO `kategori_izin` VALUES (22, 'Liburan', 90);

-- ----------------------------
-- Table structure for jns_izin
-- ----------------------------
DROP TABLE IF EXISTS `jns_izin`;
CREATE TABLE `jns_izin`  (
  `izin_jenis_id` smallint(6) NOT NULL,
  `izin_jenis_name` varchar(200) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `flag` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0: Default, 1: Normatif',
  PRIMARY KEY (`izin_jenis_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of jns_izin
-- ----------------------------
INSERT INTO `jns_izin` VALUES (10, 'Izin tidak masuk (Keperluan pribadi)', 0);
INSERT INTO `jns_izin` VALUES (20, 'Izin pulang awal (Keperluan pribadi)', 0);
INSERT INTO `jns_izin` VALUES (30, 'Izin datang terlambat (Keperluan pribadi)', 0);
INSERT INTO `jns_izin` VALUES (40, 'Sakit dengan surat dokter', 0);
INSERT INTO `jns_izin` VALUES (50, 'Sakit tanpa surat dokter', 0);
INSERT INTO `jns_izin` VALUES (60, 'Izin meninggalkan tempat kerja', 0);
INSERT INTO `jns_izin` VALUES (70, 'Izin dinas (Izin keperluan kantor)', 0);
INSERT INTO `jns_izin` VALUES (71, 'Izin datang terlambat (Keperluan kantor)', 0);
INSERT INTO `jns_izin` VALUES (72, 'Izin pulang awal (Keperluan kantor)', 0);
INSERT INTO `jns_izin` VALUES (80, 'Cuti normatif', 0);
INSERT INTO `jns_izin` VALUES (90, 'Cuti pribadi', 0);
INSERT INTO `jns_izin` VALUES (100, 'Tidak scan (masuk)', 0);
INSERT INTO `jns_izin` VALUES (101, 'Tidak scan (pulang)', 0);
INSERT INTO `jns_izin` VALUES (102, 'Tidak scan (mulai istirahat)', 0);
INSERT INTO `jns_izin` VALUES (103, 'Tidak scan (selesai istirahat)', 0);
INSERT INTO `jns_izin` VALUES (104, 'Tidak scan (mulai lembur)', 0);
INSERT INTO `jns_izin` VALUES (105, 'Tidak scan (selesai lembur)', 0);
INSERT INTO `jns_izin` VALUES (110, 'Izin lain-lain', 0);
INSERT INTO `jns_izin` VALUES (120, 'Izin libur', 0);

-- ----------------------------
-- Table structure for jdw_kerja_d
-- ----------------------------
DROP TABLE IF EXISTS `jdw_kerja_d`;
CREATE TABLE `jdw_kerja_d`  (
  `jdw_kerja_m_id` int(11) NOT NULL DEFAULT 0,
  `jdw_kerja_d_idx` smallint(6) NOT NULL DEFAULT 0 COMMENT '1:minggu; 2:senin; dst',
  `jk_id` int(11) NOT NULL DEFAULT 0,
  `jdw_kerja_d_hari` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `jdw_kerja_d_libur` tinyint(4) NULL DEFAULT 0,
  PRIMARY KEY (`jdw_kerja_m_id`, `jdw_kerja_d_idx`, `jk_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of jdw_kerja_d
-- ----------------------------
INSERT INTO `jdw_kerja_d` VALUES (1, 1, 1, 'Senin', 0);
INSERT INTO `jdw_kerja_d` VALUES (1, 2, 1, 'Selasa', 0);
INSERT INTO `jdw_kerja_d` VALUES (1, 3, 1, 'Rabu', 0);
INSERT INTO `jdw_kerja_d` VALUES (1, 4, 1, 'Kamis', 0);
INSERT INTO `jdw_kerja_d` VALUES (1, 5, 1, 'Jumat', 0);
INSERT INTO `jdw_kerja_d` VALUES (1, 6, 1, 'Sabtu', -1);
INSERT INTO `jdw_kerja_d` VALUES (1, 7, 1, 'Minggu', -1);
INSERT INTO `jdw_kerja_d` VALUES (1, 999, 1, 'Libur Umum', -1);
INSERT INTO `jdw_kerja_d` VALUES (2, 1, 2, 'Senin', 0);
INSERT INTO `jdw_kerja_d` VALUES (2, 2, 2, 'Selasa', 0);
INSERT INTO `jdw_kerja_d` VALUES (2, 3, 2, 'Rabu', 0);
INSERT INTO `jdw_kerja_d` VALUES (2, 4, 2, 'Kamis', 0);
INSERT INTO `jdw_kerja_d` VALUES (2, 5, 2, 'Jumat', 0);
INSERT INTO `jdw_kerja_d` VALUES (2, 6, 2, 'Sabtu', -1);
INSERT INTO `jdw_kerja_d` VALUES (2, 7, 0, 'Minggu', -1);
INSERT INTO `jdw_kerja_d` VALUES (2, 999, 0, 'Libur Umum', -1);

-- ----------------------------
-- Table structure for pendidikan
-- ----------------------------
DROP TABLE IF EXISTS `pendidikan`;
CREATE TABLE `pendidikan`  (
  `pend_id` int(11) NOT NULL,
  `pend_name` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  PRIMARY KEY (`pend_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of pendidikan
-- ----------------------------
INSERT INTO `pendidikan` VALUES (10, 'SD');
INSERT INTO `pendidikan` VALUES (20, 'SMP');
INSERT INTO `pendidikan` VALUES (30, 'SMA');
INSERT INTO `pendidikan` VALUES (31, 'D I');
INSERT INTO `pendidikan` VALUES (33, 'D III');
INSERT INTO `pendidikan` VALUES (34, 'D IV');
INSERT INTO `pendidikan` VALUES (40, 'S1');
INSERT INTO `pendidikan` VALUES (50, 'S2');
INSERT INTO `pendidikan` VALUES (60, 'S3');

-- ----------------------------
-- Table structure for cuti_normatif
-- ----------------------------
DROP TABLE IF EXISTS `cuti_normatif`;
CREATE TABLE `cuti_normatif`  (
  `cuti_n_id` int(11) NOT NULL DEFAULT 0,
  `cuti_n_nama` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `cuti_n_lama` smallint(6) NOT NULL DEFAULT 0,
  `nominal` float NOT NULL DEFAULT 0,
  `jns_bayar` tinyint(4) NOT NULL DEFAULT 0,
  PRIMARY KEY (`cuti_n_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of cuti_normatif
-- ----------------------------
INSERT INTO `cuti_normatif` VALUES (1, 'Cuti Hamil', 45, 0, 1);
INSERT INTO `cuti_normatif` VALUES (2, 'Cuti Menikah', 3, 0, 0);
INSERT INTO `cuti_normatif` VALUES (3, 'Cuti Khitan Anak', 2, 0, 1);
INSERT INTO `cuti_normatif` VALUES (4, 'Cuti Menikahkan Anak', 2, 0, 1);
INSERT INTO `cuti_normatif` VALUES (5, 'Cuti Keluarga Meninggal', 1, 0, 1);
INSERT INTO `cuti_normatif` VALUES (6, 'Cuti Pindah Rumah', 3, 0, 1);
INSERT INTO `cuti_normatif` VALUES (7, 'Cuti Membabtiskan Anak', 2, 0, 1);

-- ----------------------------
-- Table structure for server
-- ----------------------------
DROP TABLE IF EXISTS `server`;
CREATE TABLE `server`  (
  `id_server` int(11) NOT NULL,
  `nama_server` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `url_server` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id_server`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of server
-- ----------------------------
INSERT INTO `server` VALUES (1, 'FingerspotCloud.Net', 'https://www.fingerspotcloud.net/');
INSERT INTO `server` VALUES (2, 'KitaCloud', 'https://www.kitacloud.com/');
INSERT INTO `server` VALUES (3, 'Fingerspot.net', 'https://www.fingerspot.net/');
INSERT INTO `server` VALUES (4, 'Fingerspot.IO', 'https://api-ds.fingerspot.io/');
INSERT INTO `server` VALUES (5, 'RevoTime', 'https://revotime.id/');
INSERT INTO `server` VALUES (6, 'TimeClock', 'https://timeclock.id/');

-- ----------------------------
-- Table structure for user_login
-- ----------------------------
DROP TABLE IF EXISTS `user_login`;
CREATE TABLE `user_login`  (
  `login_id` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `login_pwd` varchar(32) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `grp_user_id` tinyint(4) NOT NULL DEFAULT 1,
  `lastupdate_date` datetime NOT NULL,
  `lastupdate_user` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  PRIMARY KEY (`login_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_login
-- ----------------------------
INSERT INTO `user_login` VALUES ('admin', 'E5DDE9D8AD34B40E457D741FBC6EA04F', 1, '2024-12-18 00:00:00', 'Fingerspot');
INSERT INTO `user_login` VALUES ('fingerspot', 'C535676C1656F0768DCB9ADFB5B75D13', 1, '2014-06-25 11:56:47', 'Fingerspot');
INSERT INTO `user_login` VALUES ('operator', '6907A7A65E7BBBFFF470C0EED3DB1446', 3, '2015-05-30 00:00:00', 'admin');
INSERT INTO `user_login` VALUES ('sub admin', 'CE1A93177040C9A5E536BA80E609A82B', 2, '2015-05-30 00:00:00', 'admin');

-- ----------------------------
-- Table structure for jam_kerja
-- ----------------------------
DROP TABLE IF EXISTS `jam_kerja`;
CREATE TABLE `jam_kerja`  (
  `jk_id` int(11) NOT NULL DEFAULT 0,
  `jk_name` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `jk_kode` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `use_set` tinyint(4) NOT NULL DEFAULT 0 COMMENT 'Yes/No',
  `jk_bcin` time NOT NULL DEFAULT '00:00:00',
  `jk_cin` smallint(6) NOT NULL DEFAULT 0,
  `jk_ecin` smallint(6) NOT NULL DEFAULT 0,
  `jk_tol_late` smallint(6) NOT NULL DEFAULT 0,
  `jk_use_ist` tinyint(4) NOT NULL DEFAULT 0 COMMENT 'Yes/No',
  `jk_ist1` time NOT NULL DEFAULT '00:00:00',
  `jk_ist2` time NOT NULL DEFAULT '00:00:00',
  `jk_tol_early` smallint(6) NOT NULL DEFAULT 0,
  `jk_bcout` smallint(6) NOT NULL DEFAULT 0,
  `jk_cout` smallint(6) NOT NULL DEFAULT 0,
  `jk_ecout` time NOT NULL DEFAULT '00:00:00',
  `use_eot` tinyint(4) NOT NULL DEFAULT 0,
  `min_eot` smallint(6) NOT NULL DEFAULT 0,
  `max_eot` smallint(6) NOT NULL DEFAULT 0,
  `reduce_eot` smallint(6) NOT NULL DEFAULT 0,
  `jk_durasi` tinyint(4) NOT NULL DEFAULT 0 COMMENT '1: Efektif, 2: Aktual',
  `jk_countas` float NOT NULL DEFAULT 0,
  `jk_min_countas` smallint(6) NOT NULL DEFAULT 0,
  `jk_min_countas2` smallint(6) NOT NULL DEFAULT 0,
  `jk_ket` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '',
  PRIMARY KEY (`jk_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of jam_kerja
-- ----------------------------
INSERT INTO `jam_kerja` VALUES (1, '07.00-13.00', '1', -1, '06:00:00', 60, 60, 30, 0, '00:00:00', '00:00:00', 0, 60, 120, '15:00:00', 0, 0, 0, 0, 1, 1, 60, 120, 'PENDIDIK');
INSERT INTO `jam_kerja` VALUES (2, '08.30', '2', -1, '06:30:00', 60, 60, 0, 0, '00:00:00', '00:00:00', 0, 60, 60, '12:00:00', 0, 0, 0, 0, 1, 1, 60, 120, 'PESERTA DIDIK');

-- ----------------------------
-- Table structure for device
-- ----------------------------
DROP TABLE IF EXISTS `device`;
CREATE TABLE `device`  (
  `sn` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `activation_code` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `act_code_realtime` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL,
  `device_name` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '',
  `dev_id` smallint(6) NOT NULL DEFAULT 1 COMMENT 'no mesin',
  `comm_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0: ethernet, 1: usb, 2: serial',
  `ip_address` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '',
  `id_type` int(11) NOT NULL DEFAULT 0,
  `dev_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT 'Jenis mesin = 0: ZK, 1: Hanvon, 2: Realand',
  `comm_key` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '0' COMMENT 'Password koneksi mesin',
  `serial_port` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '',
  `baud_rate` varchar(15) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT '',
  `ethernet_port` varchar(30) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '4370',
  `layar` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0: TFT, 1: BW',
  `alg_ver` tinyint(4) NOT NULL DEFAULT 10 COMMENT '9 & 10',
  `use_realtime` tinyint(4) NOT NULL DEFAULT 0 COMMENT 'yes/no',
  `group_realtime` tinyint(4) NOT NULL DEFAULT 0,
  `last_download` date NULL DEFAULT NULL,
  `ATTLOGStamp` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `OPERLOGStamp` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `ATTPHOTOStamp` varchar(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '0',
  `cloud_id` varchar(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT '',
  `last_download_web` date NULL DEFAULT NULL,
  `id_server_use` int(11) NOT NULL DEFAULT -1,
  PRIMARY KEY (`sn`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of device
-- ----------------------------
INSERT INTO `device` VALUES ('66208023321907', '4F1A2-0903-3EC52-DA77-6684D-0274-0AA3B', NULL, 'Mesin 2', 1, 0, '192.168.65.120', 29, 3, '0', '', '', '5005', 0, 10, 0, 0, '2025-02-15', '0', '0', '0', '', NULL, -1);
INSERT INTO `device` VALUES ('FIO66205020150662', 'BB620-B0BA-261A5-F6FD-DB515-077C-9BF06', NULL, 'Mesin 1', 1, 0, '192.168.18.201', 29, 3, '0', '', '', '5005', 0, 10, 0, 0, '2025-01-25', '0', '0', '0', '', NULL, -1);

SET FOREIGN_KEY_CHECKS = 1;
