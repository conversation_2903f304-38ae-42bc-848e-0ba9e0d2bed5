<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Route Links Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .module-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .module-title {
            color: #2c3e50;
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        .link-list {
            list-style: none;
            padding: 0;
        }
        .link-list li {
            margin: 8px 0;
        }
        .link-list a {
            display: inline-block;
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
            min-width: 200px;
            text-align: center;
        }
        .link-list a:hover {
            background-color: #2980b9;
        }
        .description {
            color: #666;
            font-size: 0.9em;
            margin-left: 10px;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Route Links Test Page</h1>
        
        <div class="status">
            <strong>✅ Test Status:</strong> All route files are properly configured and have valid PHP syntax.
        </div>

        <div class="module-section">
            <div class="module-title">🏠 Main Application</div>
            <ul class="link-list">
                <li>
                    <a href="/" target="_blank">Home Page</a>
                    <span class="description">- Main application homepage</span>
                </li>
            </ul>
        </div>

        <div class="module-section">
            <div class="module-title">👥 Student Management Module</div>
            <ul class="link-list">
                <li>
                    <a href="/students" target="_blank">Students List</a>
                    <span class="description">- View all students</span>
                </li>
                <li>
                    <a href="/students/create" target="_blank">Add Student</a>
                    <span class="description">- Create new student</span>
                </li>
                <li>
                    <a href="/students/sessions" target="_blank">Sessions</a>
                    <span class="description">- Manage academic sessions</span>
                </li>
                <li>
                    <a href="/students/classes" target="_blank">Classes</a>
                    <span class="description">- Manage classes</span>
                </li>
                <li>
                    <a href="/students/sections" target="_blank">Sections</a>
                    <span class="description">- Manage sections</span>
                </li>
            </ul>
        </div>

        <div class="module-section">
            <div class="module-title">📊 Attendance Module</div>
            <ul class="link-list">
                <li>
                    <a href="/attendance" target="_blank">Attendance Dashboard</a>
                    <span class="description">- Main attendance page</span>
                </li>
                <li>
                    <a href="/attendance/mark" target="_blank">Mark Attendance</a>
                    <span class="description">- Mark student attendance</span>
                </li>
                <li>
                    <a href="/attendance/reports" target="_blank">Reports</a>
                    <span class="description">- Attendance reports</span>
                </li>
                <li>
                    <a href="/attendance/devices" target="_blank">Devices</a>
                    <span class="description">- Manage attendance devices</span>
                </li>
                <li>
                    <a href="/attendance/reports/daily" target="_blank">Daily Report</a>
                    <span class="description">- Daily attendance report</span>
                </li>
                <li>
                    <a href="/attendance/reports/monthly" target="_blank">Monthly Report</a>
                    <span class="description">- Monthly attendance report</span>
                </li>
            </ul>
        </div>

        <div style="margin-top: 30px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; color: #856404;">
            <strong>📝 Instructions:</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Click on any link above to test the route</li>
                <li>Links open in new tabs for easy testing</li>
                <li>If a route works, you'll see the corresponding page</li>
                <li>If a route fails, you'll see a CodeIgniter error page</li>
                <li>Make sure your development server is running</li>
            </ul>
        </div>

        <div style="margin-top: 20px; text-align: center; color: #666; font-size: 0.9em;">
            <p>Generated by Route Testing System | StudentFinger Application</p>
        </div>
    </div>

    <script>
        // Add click tracking for testing
        document.querySelectorAll('a[href^="/"]').forEach(link => {
            link.addEventListener('click', function(e) {
                console.log('Testing route:', this.href);
                // You can add additional tracking here if needed
            });
        });
    </script>
</body>
</html>