<?php

namespace App\Models;

use CodeIgniter\Model;

class AttendanceModel extends Model
{
    protected $table = 'att_log';
    protected $primaryKey = 'att_id';
    protected $returnType = 'array';
    protected $useSoftDeletes = false; // att_log doesn't have soft deletes initially
    protected $useTimestamps = false; // att_log doesn't have timestamps initially

    protected $allowedFields = [
        'pin',
        'scan_date',
        'verifymode',
        'status',
        'serialnumber',
        'student_id'
    ];
    
    protected $validationRules = [
        'pin' => 'permit_empty|max_length[50]',
        'scan_date' => 'required|valid_date[Y-m-d H:i:s]',
        'verifymode' => 'permit_empty|integer|in_list[0,1,2]',
        'status' => 'permit_empty|integer|in_list[0,1,2,3]',
        'serialnumber' => 'permit_empty|max_length[50]',
        'student_id' => 'permit_empty|integer'
    ];

    protected $validationMessages = [
        'scan_date' => [
            'required' => 'Scan date is required',
            'valid_date' => 'Please provide a valid date and time'
        ],
        'verifymode' => [
            'in_list' => 'Verify mode must be 0 (Fingerprint), 1 (RFID), or 2 (Face)'
        ],
        'status' => [
            'in_list' => 'Status must be 0 (Absent), 1 (Present), 2 (Late), or 3 (Permission)'
        ]
    ];
    
    /**
     * Get attendance records with student information
     */
    public function getAttendanceWithStudents($filters = [])
    {
        $builder = $this->db->table($this->table)
            ->select('att_log.*, students.firstname, students.lastname, students.student_id as student_code')
            ->join('students', 'students.id = att_log.student_id', 'left');

        // Apply filters
        if (!empty($filters['search'])) {
            $builder->groupStart()
                ->like('students.firstname', $filters['search'])
                ->orLike('students.lastname', $filters['search'])
                ->orLike('students.student_id', $filters['search'])
                ->orLike('att_log.pin', $filters['search'])
                ->groupEnd();
        }

        if (!empty($filters['date_from'])) {
            $builder->where('DATE(att_log.scan_date) >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $builder->where('DATE(att_log.scan_date) <=', $filters['date_to']);
        }

        if (!empty($filters['status'])) {
            $builder->where('att_log.status', $filters['status']);
        }

        if (!empty($filters['verifymode'])) {
            $builder->where('att_log.verifymode', $filters['verifymode']);
        }

        return $builder->orderBy('att_log.scan_date', 'DESC');
    }
    
    /**
     * Get attendance statistics
     */
    public function getAttendanceStats($dateFrom = null, $dateTo = null)
    {
        $builder = $this->db->table($this->table)
            ->select('status, COUNT(*) as count');

        if ($dateFrom) {
            $builder->where('DATE(scan_date) >=', $dateFrom);
        }

        if ($dateTo) {
            $builder->where('DATE(scan_date) <=', $dateTo);
        }

        return $builder->groupBy('status')->get()->getResultArray();
    }

    /**
     * Get daily attendance summary
     */
    public function getDailyAttendanceSummary($date)
    {
        return $this->db->table($this->table)
            ->select('status, COUNT(*) as count')
            ->where('DATE(scan_date)', $date)
            ->groupBy('status')
            ->get()
            ->getResultArray();
    }

    /**
     * Get status labels for display
     */
    public function getStatusLabel($status)
    {
        $labels = [
            0 => 'Absent',
            1 => 'Present',
            2 => 'Late',
            3 => 'Permission'
        ];

        return $labels[$status] ?? 'Unknown';
    }

    /**
     * Get verify mode labels for display
     */
    public function getVerifyModeLabel($verifymode)
    {
        $labels = [
            0 => 'Fingerprint',
            1 => 'RFID',
            2 => 'Face'
        ];

        return $labels[$verifymode] ?? 'Unknown';
    }
    
    /**
     * Get student attendance history
     */
    public function getStudentAttendanceHistory($studentId, $limit = 30)
    {
        return $this->where('student_id', $studentId)
                   ->orderBy('scan_date', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Check if attendance exists for student on date
     */
    public function attendanceExists($studentId, $date)
    {
        return $this->where('student_id', $studentId)
                   ->where('DATE(scan_date)', $date)
                   ->countAllResults() > 0;
    }

    /**
     * Get recent attendance logs
     */
    public function getRecentAttendance($limit = 10)
    {
        return $this->select('att_log.*, students.firstname, students.lastname, students.student_id as student_code')
                   ->join('students', 'students.id = att_log.student_id', 'left')
                   ->orderBy('scan_date', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }
    
    /**
     * Get monthly attendance report
     */
    public function getMonthlyReport($year, $month)
    {
        $builder = $this->db->table($this->table)
            ->select('att_log.*, students.firstname, students.lastname, students.student_id as student_code')
            ->join('students', 'students.id = att_log.student_id', 'left')
            ->where('YEAR(att_log.scan_date)', $year)
            ->where('MONTH(att_log.scan_date)', $month);

        return $builder->orderBy('att_log.scan_date', 'ASC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Bulk insert attendance logs
     */
    public function bulkInsertLogs($attendanceData)
    {
        return $this->insertBatch($attendanceData);
    }

    /**
     * Get attendance by device serial number
     */
    public function getAttendanceByDevice($serialNumber, $dateFrom = null, $dateTo = null)
    {
        $builder = $this->where('serialnumber', $serialNumber);

        if ($dateFrom) {
            $builder->where('DATE(scan_date) >=', $dateFrom);
        }

        if ($dateTo) {
            $builder->where('DATE(scan_date) <=', $dateTo);
        }

        return $builder->orderBy('scan_date', 'DESC')
                      ->findAll();
    }

    /**
     * Get today's attendance summary for dashboard
     */
    public function getTodaysSummary()
    {
        $today = date('Y-m-d');
        $stats = $this->getDailyAttendanceSummary($today);

        $summary = [
            'present' => 0,
            'absent' => 0,
            'late' => 0,
            'permission' => 0,
            'total' => 0
        ];

        foreach ($stats as $stat) {
            $summary['total'] += $stat['count'];
            switch ($stat['status']) {
                case 1:
                    $summary['present'] = $stat['count'];
                    break;
                case 0:
                    $summary['absent'] = $stat['count'];
                    break;
                case 2:
                    $summary['late'] = $stat['count'];
                    break;
                case 3:
                    $summary['permission'] = $stat['count'];
                    break;
            }
        }

        return $summary;
    }
}