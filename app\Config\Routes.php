<?php
// Class Routes (Generated by CRUDL)
$routes->group('classes', function($routes) {
    $routes->get('/', 'ClassController::index');
    $routes->get('create', 'ClassController::create');
    $routes->post('create', 'ClassController::create');
    $routes->get('view/(:segment)', 'ClassController::view/$1');
    $routes->get('edit/(:segment)', 'ClassController::edit/$1');
    $routes->post('edit/(:segment)', 'ClassController::edit/$1');
    $routes->get('delete/(:segment)', 'ClassController::delete/$1');
});


use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
// Home routes
$routes->get('/', 'HomeController::index');
$routes->get('home', 'HomeController::index');

// Student Management routes
$routes->group('students', function($routes) {
    $routes->get('/', 'StudentsController::index');
    $routes->get('create', 'StudentsController::create');
    $routes->post('store', 'StudentsController::store');
    $routes->get('show/(:num)', 'StudentsController::show/$1');
    $routes->get('edit/(:num)', 'StudentsController::edit/$1');
    $routes->post('update/(:num)', 'StudentsController::update/$1');
    $routes->delete('delete/(:num)', 'StudentsController::delete/$1');
    $routes->get('search', 'StudentsController::search');
    $routes->get('filter', 'StudentsController::filter');
});

// Attendance routes
$routes->group('attendance', function($routes) {
    $routes->get('/', 'AttendanceController::index');
    $routes->get('mark', 'AttendanceController::mark');
    $routes->post('store', 'AttendanceController::store');
    $routes->get('report', 'AttendanceController::report');
    $routes->get('student/(:num)', 'AttendanceController::studentAttendance/$1');
    $routes->get('class/(:num)', 'AttendanceController::classAttendance/$1');
    $routes->get('export', 'AttendanceController::export');
});

// Table Manager Routes
$routes->group('table-manager', function($routes) {
    $routes->get('/', 'TableManagerController::index');
    $routes->get('create', 'TableManagerController::create');
    $routes->post('create', 'TableManagerController::create');
    $routes->get('edit/(:segment)', 'TableManagerController::edit/$1');
    $routes->post('edit/(:segment)', 'TableManagerController::edit/$1');
    $routes->get('delete/(:segment)', 'TableManagerController::delete/$1');
    $routes->post('generate-crudl', 'TableManagerController::generateCrudl');
});

// Dashboard Routes
$routes->get('dashboard', 'DashboardController::index');

// Test Table Routes (Generated by CRUDL)
$routes->group('testtables', function($routes) {
    $routes->get('/', 'TestTableController::index');
    $routes->get('create', 'TestTableController::create');
    $routes->post('create', 'TestTableController::create');
    $routes->get('view/(:num)', 'TestTableController::view/$1');
    $routes->get('edit/(:num)', 'TestTableController::edit/$1');
    $routes->post('edit/(:num)', 'TestTableController::edit/$1');
    $routes->get('delete/(:num)', 'TestTableController::delete/$1');
});

// Attendance Log Routes (Generated by CRUDL)
$routes->group('attendance-logs', function($routes) {
    $routes->get('/', 'AttendanceLogController::index');
    $routes->get('create', 'AttendanceLogController::create');
    $routes->post('create', 'AttendanceLogController::create');
    $routes->get('view/(:segment)', 'AttendanceLogController::view/$1');
    $routes->get('edit/(:segment)', 'AttendanceLogController::edit/$1');
    $routes->post('edit/(:segment)', 'AttendanceLogController::edit/$1');
    $routes->get('delete/(:segment)', 'AttendanceLogController::delete/$1');
});

// User Log Routes (Generated by CRUDL)
$routes->group('user-logs', function($routes) {
    $routes->get('/', 'UserLogController::index');
    $routes->get('create', 'UserLogController::create');
    $routes->post('create', 'UserLogController::create');
    $routes->get('view/(:segment)', 'UserLogController::view/$1');
    $routes->get('edit/(:segment)', 'UserLogController::edit/$1');
    $routes->post('edit/(:segment)', 'UserLogController::edit/$1');
    $routes->get('delete/(:segment)', 'UserLogController::delete/$1');
});
