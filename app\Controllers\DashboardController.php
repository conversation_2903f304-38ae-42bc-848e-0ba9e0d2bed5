<?php

namespace App\Controllers;

use App\Models\AttendanceModel;
use App\Models\StudentModel;

class DashboardController extends BaseController
{
    protected $attendanceModel;
    protected $studentModel;

    public function __construct()
    {
        $this->attendanceModel = new AttendanceModel();
        $this->studentModel = new StudentModel();
    }

    public function index()
    {
        $data['title'] = 'Dashboard';

        // Get real statistics
        $todaysSummary = $this->attendanceModel->getTodaysSummary();
        $data['totalStudents'] = $this->studentModel->countAllResults();
        $data['presentToday'] = $todaysSummary['present'];
        $data['absentToday'] = $todaysSummary['absent'];
        $data['lateToday'] = $todaysSummary['late'];

        // Get recent attendance from att_log
        $recentLogs = $this->attendanceModel->getRecentAttendance(10);
        $data['recentAttendance'] = [];

        foreach ($recentLogs as $log) {
            $data['recentAttendance'][] = [
                'student_id' => $log['student_code'] ?? $log['pin'],
                'name' => trim(($log['firstname'] ?? '') . ' ' . ($log['lastname'] ?? '')) ?: 'Unknown',
                'time_in' => date('H:i A', strtotime($log['scan_date'])),
                'status' => $this->attendanceModel->getStatusLabel($log['status']),
                'verify_mode' => $this->attendanceModel->getVerifyModeLabel($log['verifymode'])
            ];
        }

        return view('dashboard/index', $data);
    }
}