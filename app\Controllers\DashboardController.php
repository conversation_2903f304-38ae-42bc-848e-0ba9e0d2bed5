<?php

namespace App\Controllers;

class DashboardController extends BaseController
{
    public function index()
    {
        $data['title'] = 'Dashboard';
        
        // Get statistics
        $data['totalStudents'] = 1234; // Replace with actual data
        $data['presentToday'] = 890;
        $data['absentToday'] = 45;
        $data['lateToday'] = 23;
        
        // Get recent attendance
        $data['recentAttendance'] = [
            [
                'student_id' => 'STU001',
                'name' => '<PERSON>',
                'time_in' => '08:00 AM',
                'status' => 'Present'
            ],
            [
                'student_id' => 'STU002',
                'name' => '<PERSON>',
                'time_in' => '08:15 AM',
                'status' => 'Late'
            ],
            [
                'student_id' => 'STU003',
                'name' => '<PERSON>',
                'time_in' => '--:--',
                'status' => 'Absent'
            ]
        ];
        
        return view('dashboard/index', $data);
    }
} 